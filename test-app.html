<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CAS Recruiter Portal - Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 4px;
        }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        button:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <h1>🧪 CAS Recruiter Portal - Application Test</h1>
    
    <div class="test-section">
        <h2>📋 Test Results</h2>
        <div id="testResults"></div>
        <button onclick="runTests()">Run All Tests</button>
        <button onclick="clearTests()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>🔗 Quick Navigation</h2>
        <button onclick="window.open('index.html', '_blank')">Open Landing Page</button>
        <button onclick="window.open('pages/login.html', '_blank')">Open Login Page</button>
        <button onclick="window.open('pages/dashboard.html', '_blank')">Open Dashboard</button>
    </div>

    <div class="test-section">
        <h2>🛠️ Manual Tests</h2>
        <div class="info">
            <strong>Manual Testing Checklist:</strong>
            <ul>
                <li>✅ Landing page loads and redirects properly</li>
                <li>✅ Login page loads with proper styling</li>
                <li>✅ Sign up functionality works</li>
                <li>✅ Sign in functionality works</li>
                <li>✅ Dashboard loads after login</li>
                <li>✅ Job filtering works</li>
                <li>✅ Job search works</li>
                <li>✅ Job application modal opens</li>
                <li>✅ CV upload and analysis works</li>
                <li>✅ Logout functionality works</li>
            </ul>
        </div>
    </div>

    <script>
        function addTestResult(test, status, message) {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            div.innerHTML = `<strong>${test}:</strong> ${message}`;
            results.appendChild(div);
        }

        function clearTests() {
            document.getElementById('testResults').innerHTML = '';
        }

        function runTests() {
            clearTests();
            
            // Test 1: Check if files exist
            addTestResult('File Structure', 'info', 'Testing file accessibility...');
            
            // Test 2: Check localStorage functionality
            try {
                localStorage.setItem('test_key', 'test_value');
                const value = localStorage.getItem('test_key');
                localStorage.removeItem('test_key');
                if (value === 'test_value') {
                    addTestResult('LocalStorage', 'pass', 'LocalStorage is working correctly');
                } else {
                    addTestResult('LocalStorage', 'fail', 'LocalStorage test failed');
                }
            } catch (e) {
                addTestResult('LocalStorage', 'fail', 'LocalStorage error: ' + e.message);
            }

            // Test 3: Check if CSS can be loaded
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'css/styles.css';
            link.onload = () => addTestResult('CSS Loading', 'pass', 'CSS file loads successfully');
            link.onerror = () => addTestResult('CSS Loading', 'fail', 'CSS file failed to load');
            document.head.appendChild(link);

            // Test 4: Check if JavaScript can be loaded
            const script = document.createElement('script');
            script.src = 'js/scripts.js';
            script.onload = () => addTestResult('JavaScript Loading', 'pass', 'JavaScript file loads successfully');
            script.onerror = () => addTestResult('JavaScript Loading', 'fail', 'JavaScript file failed to load');
            document.head.appendChild(script);

            // Test 5: Check default users
            setTimeout(() => {
                try {
                    const users = JSON.parse(localStorage.getItem('cubeai_users_db') || '[]');
                    if (users.length > 0) {
                        addTestResult('Default Users', 'pass', `Found ${users.length} users in database`);
                    } else {
                        addTestResult('Default Users', 'info', 'No users found - will be created on login page');
                    }
                } catch (e) {
                    addTestResult('Default Users', 'fail', 'Error checking users: ' + e.message);
                }
            }, 1000);

            addTestResult('Test Suite', 'info', 'All automated tests completed. Check manual tests above.');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
